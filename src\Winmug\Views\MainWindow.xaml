<Window x:Class="Winmug.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Winmug.Converters"
        mc:Ignorable="d"
        Title="Winmug - SmugMug Photo Downloader"
        Height="700" Width="1000"
        MinHeight="600" MinWidth="800"
        WindowStartupLocation="CenterScreen"
        Background="#F8F9FA">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="Winmug - SmugMug Photo Downloader"
                       FontSize="24" FontWeight="Bold"
                       HorizontalAlignment="Center"/>
            <TextBlock Text="Download your entire SmugMug photo library to your local computer"
                       FontSize="14" Foreground="Gray"
                       HorizontalAlignment="Center" Margin="0,5,0,0"/>
        </StackPanel>

        <!-- Authentication Section with Curved Edges -->
        <Border Grid.Row="1" Background="White" BorderBrush="#E0E0E0" BorderThickness="1"
                CornerRadius="15" Padding="20" Margin="0,0,0,20">
            <StackPanel>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- User Profile Section (when authenticated) -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center"
                                Visibility="{Binding IsAuthenticated, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <!-- Profile Picture -->
                        <Border Width="60" Height="60" CornerRadius="30" Background="#4CAF50" Margin="0,0,15,0">
                            <TextBlock Text="{Binding UserDisplayName, Converter={x:Static local:FirstLetterConverter.Instance}}"
                                       FontSize="22" FontWeight="Bold" Foreground="White"
                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>

                        <!-- User Info and Action Buttons -->
                        <StackPanel VerticalAlignment="Center">
                            <TextBlock Text="{Binding UserDisplayName}" FontSize="18" FontWeight="Bold" Foreground="#2E7D32"/>
                            <TextBlock Text="{Binding UserStatusText}" FontSize="11" Foreground="#666666" Margin="0,2,0,8"/>

                            <!-- Action Buttons below profile -->
                            <StackPanel Orientation="Horizontal">
                                <Button Content="Show my albums"
                                        Command="{Binding LoadFolderStructureCommand}"
                                        Margin="0,0,10,0" Padding="12,6" FontSize="11"
                                        Background="#1976D2" Foreground="White" BorderThickness="0"/>
                                <Button Content="Logout"
                                        Command="{Binding LogoutCommand}"
                                        Padding="12,6" FontSize="11"
                                        Background="#F44336" Foreground="White" BorderThickness="0"/>
                            </StackPanel>
                        </StackPanel>
                    </StackPanel>

                    <!-- Authentication Prompt (when not authenticated) -->
                    <StackPanel Grid.Column="1" VerticalAlignment="Center" HorizontalAlignment="Center"
                                Visibility="{Binding IsAuthenticated, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                        <TextBlock Text="Please authenticate to access your SmugMug photos"
                                   FontSize="16" Foreground="#666666" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <Button Content="Authenticate with SmugMug"
                                Command="{Binding InitiateAuthenticationCommand}"
                                Padding="20,10" FontSize="14"
                                Background="#2E7D32" Foreground="White" BorderThickness="0"/>
                    </StackPanel>
                </Grid>

                <!-- Verification Code Input (only visible when waiting) -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,15,0,0"
                            Visibility="{Binding IsWaitingForVerificationCode, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <TextBlock Text="Enter verification code:" VerticalAlignment="Center" Margin="0,0,10,0" FontSize="12"/>
                    <TextBox x:Name="VerificationCodeTextBox" Width="120" Margin="0,0,10,0" Padding="6,4"/>
                    <Button Content="Submit"
                            Command="{Binding CompleteAuthenticationCommand}"
                            CommandParameter="{Binding ElementName=VerificationCodeTextBox, Path=Text}"
                            Padding="12,4" Background="#4CAF50" Foreground="White" BorderThickness="0"/>
                </StackPanel>

                <!-- Progress Section -->
                <Grid Margin="0,15,0,0"
                      Visibility="{Binding IsLoadingFolderStructure, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Status Message -->
                    <TextBlock Grid.Row="0" Text="{Binding StatusMessage}"
                               FontWeight="Bold" Margin="0,0,0,5" HorizontalAlignment="Center" FontSize="12"/>

                    <!-- Progress Bar -->
                    <ProgressBar Grid.Row="1" Value="{Binding AlbumsFoundCount}"
                                 Maximum="{Binding AlbumsFoundMaximum}"
                                 Height="6" Margin="0,0,0,3"
                                 Background="#E0E0E0" Foreground="#4CAF50"/>

                    <!-- Progress Text -->
                    <TextBlock Grid.Row="2" Text="{Binding AlbumDiscoveryProgress}"
                               HorizontalAlignment="Center" FontSize="10" Foreground="#666666"/>
                </Grid>
            </StackPanel>
        </Border>

        <!-- Albums Section -->
        <GroupBox Grid.Row="2" Header="Albums" Margin="0,0,0,10"
                  Visibility="{Binding IsFolderStructureLoaded, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Albums Header with Center-Aligned Controls -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                    <TextBlock Text="Albums" FontSize="16" FontWeight="Bold" Margin="0,0,20,0" VerticalAlignment="Center"/>
                    <Button Content="Select all" Command="{Binding SelectAllAlbumsCommand}"
                            Padding="8,4" Margin="0,0,10,0" FontSize="11"/>
                    <Button Content="Deselect all" Command="{Binding DeselectAllAlbumsCommand}"
                            Padding="8,4" FontSize="11"/>
                </StackPanel>

                <!-- Album Count Info -->
                <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                    <TextBlock Text="Total albums found: " FontSize="12" Foreground="#666666"/>
                    <TextBlock Text="{Binding TotalAlbumCount}" FontSize="12" FontWeight="Bold"/>
                    <TextBlock Text=" (" FontSize="12" Foreground="#666666" Margin="5,0,0,0"/>
                    <TextBlock Text="{Binding FolderStructure.TotalEstimatedSize, Mode=OneWay}" FontSize="12" FontWeight="Bold"/>
                    <TextBlock Text=")" FontSize="12" Foreground="#666666"/>
                </StackPanel>

                <!-- Compact Albums List with Sleek Row Design -->
                <Border Grid.Row="2" BorderBrush="#CCCCCC" BorderThickness="1" CornerRadius="3">
                    <ScrollViewer VerticalScrollBarVisibility="Auto"
                                  HorizontalScrollBarVisibility="Disabled"
                                  CanContentScroll="True">
                        <ListView ItemsSource="{Binding AlbumsView}"
                                  ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                                  ScrollViewer.VerticalScrollBarVisibility="Disabled"
                                  VirtualizingPanel.IsVirtualizing="True"
                                  VirtualizingPanel.VirtualizationMode="Recycling">
                            <ListView.ItemContainerStyle>
                                <Style TargetType="ListViewItem">
                                    <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                    <Setter Property="Padding" Value="1"/>
                                    <Setter Property="Margin" Value="0"/>
                                    <Setter Property="BorderThickness" Value="0"/>
                                </Style>
                            </ListView.ItemContainerStyle>
                            <ListView.ItemTemplate>
                                <DataTemplate>
                                    <Border BorderBrush="#F0F0F0" BorderThickness="0,0,0,1" Padding="8,4">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- Checkbox -->
                                            <CheckBox Grid.Column="0" IsChecked="{Binding IsSelected}"
                                                      VerticalAlignment="Center" Margin="0,0,8,0"/>

                                            <!-- Album Icon -->
                                            <TextBlock Grid.Column="1" Text="{Binding AlbumTypeIcon}"
                                                       FontSize="10" VerticalAlignment="Center" Margin="0,0,6,0"/>

                                            <!-- Album Name -->
                                            <TextBlock Grid.Column="2" Text="{Binding DisplayName}"
                                                       FontSize="11" FontWeight="SemiBold" VerticalAlignment="Center"
                                                       TextTrimming="CharacterEllipsis"/>

                                            <!-- Image Count -->
                                            <TextBlock Grid.Column="3" VerticalAlignment="Center" Margin="8,0">
                                                <Run Text="{Binding ImageCount}" FontSize="10" Foreground="#666666"/>
                                                <Run Text=" images" FontSize="10" Foreground="#666666"/>
                                            </TextBlock>

                                            <!-- Size -->
                                            <TextBlock Grid.Column="4" Text="{Binding EstimatedSize}"
                                                       FontSize="10" Foreground="#666666" VerticalAlignment="Center" Margin="8,0"/>

                                            <!-- Privacy & Selection Status -->
                                            <StackPanel Grid.Column="5" VerticalAlignment="Center" Orientation="Horizontal" Margin="8,0,0,0">
                                                <TextBlock Text="{Binding PrivacyIcon}" FontSize="10" Margin="0,0,4,0"
                                                           ToolTip="{Binding PrivacyStatus}"/>
                                                <TextBlock Text="✓" FontSize="12" Foreground="#4CAF50" FontWeight="Bold"
                                                           Visibility="{Binding IsSelected, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ListView.ItemTemplate>
                        </ListView>
                    </ScrollViewer>
                </Border>
            </Grid>
        </GroupBox>

        <!-- Download Configuration Section -->
        <GroupBox Grid.Row="3" Header="Download Configuration" Margin="0,0,0,10">
            <StackPanel Margin="10">
                <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                    <TextBlock Text="Target Directory:" VerticalAlignment="Center" Width="120"/>
                    <TextBox Text="{Binding TargetDirectory}"
                             IsReadOnly="True"
                             Width="400" Margin="0,0,10,0"/>
                    <Button Content="Browse..."
                            Command="{Binding SelectTargetDirectoryCommand}"
                            Padding="10,5"/>
                </StackPanel>

                <!-- Center-Aligned Download Section -->
                <StackPanel HorizontalAlignment="Center" Margin="0,10,0,0">
                    <!-- Download Button (when not downloading) -->
                    <Button Content="Start download" FontSize="16" Padding="30,12" Margin="0,0,0,10"
                            Command="{Binding StartDownloadCommand}"
                            IsEnabled="{Binding CanStartDownload}"
                            Background="#4CAF50" Foreground="White" BorderThickness="0"
                            Visibility="{Binding IsDownloading, Converter={StaticResource InverseBooleanToVisibilityConverter}}"/>

                    <!-- Download Progress (when downloading) -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10"
                                Visibility="{Binding IsDownloading, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel VerticalAlignment="Center" Margin="0,0,20,0">
                            <TextBlock Text="{Binding ProgressText}" FontSize="14" FontWeight="Bold"
                                       HorizontalAlignment="Center" Margin="0,0,0,5"/>
                            <ProgressBar Width="300" Height="8" Value="{Binding OverallProgress}" Maximum="100"
                                         Background="#E0E0E0" Foreground="#4CAF50"/>
                        </StackPanel>

                        <!-- Control Buttons (when downloading) -->
                        <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                            <Button Content="Pause" FontSize="12" Padding="12,6" Margin="0,0,5,0"
                                    Command="{Binding PauseDownloadCommand}"
                                    IsEnabled="{Binding CanPauseDownload}"
                                    Background="#FF9800" Foreground="White" BorderThickness="0"/>

                            <Button Content="Cancel" FontSize="12" Padding="12,6"
                                    Command="{Binding CancelDownloadCommand}"
                                    IsEnabled="{Binding CanCancelDownload}"
                                    Background="#F44336" Foreground="White" BorderThickness="0"/>
                        </StackPanel>
                    </StackPanel>
                </StackPanel>
            </StackPanel>
        </GroupBox>
    </Grid>
</Window>
