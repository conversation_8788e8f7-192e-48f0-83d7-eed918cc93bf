<Window x:Class="Winmug.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="Winmug - SmugMug Photo Downloader" 
        Height="600" Width="900"
        MinHeight="500" MinWidth="700"
        WindowStartupLocation="CenterScreen">
    
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="Winmug - SmugMug Photo Downloader" 
                       FontSize="24" FontWeight="Bold" 
                       HorizontalAlignment="Center"/>
            <TextBlock Text="Download your entire SmugMug photo library to your local computer" 
                       FontSize="14" Foreground="Gray" 
                       HorizontalAlignment="Center" Margin="0,5,0,0"/>
        </StackPanel>

        <!-- Authentication Section -->
        <GroupBox Grid.Row="1" Header="Authentication" Margin="0,0,0,10">
            <StackPanel Margin="10">
                <TextBlock Text="{Binding AuthenticationStatus}" 
                           FontWeight="Bold" Margin="0,0,0,10"/>
                
                <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                    <Button Content="Authenticate with SmugMug"
                            Command="{Binding InitiateAuthenticationCommand}"
                            IsEnabled="{Binding IsAuthenticated, Converter={StaticResource InverseBooleanConverter}}"
                            Margin="0,0,10,0" Padding="10,5"/>

                    <Button Content="Logout"
                            Command="{Binding LogoutCommand}"
                            IsEnabled="{Binding IsAuthenticated}"
                            Margin="0,0,10,0" Padding="10,5"/>

                    <Button Content="Load Folder Structure"
                            Command="{Binding LoadFolderStructureCommand}"
                            IsEnabled="{Binding IsAuthenticated}"
                            Padding="10,5"
                            Margin="10,0,10,0"/>

                    <!-- Progress indicator for folder structure loading -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,0"
                                Visibility="{Binding IsLoadingFolderStructure, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <ProgressBar Width="200" Height="20"
                                     Background="LightGray"
                                     Foreground="Green"
                                     Value="{Binding AlbumsFoundCount}"
                                     Maximum="{Binding AlbumsFoundMaximum}"
                                     Margin="0,0,10,0"/>
                        <TextBlock Text="{Binding AlbumDiscoveryProgress}"
                                   VerticalAlignment="Center"
                                   FontWeight="Bold"
                                   Foreground="Green"/>
                    </StackPanel>
                </StackPanel>

                <!-- Verification Code Input (shown when waiting for verification code) -->
                <StackPanel Orientation="Horizontal" Margin="0,0,0,10"
                            Visibility="{Binding IsWaitingForVerificationCode, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <TextBlock Text="6-digit verification code:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBox x:Name="VerificationCodeTextBox" Width="150" Margin="0,0,10,0"
                             ToolTip="Enter the 6-digit code from SmugMug after logging in"/>
                    <Button Content="Complete Authentication"
                            Command="{Binding CompleteAuthenticationCommand}"
                            CommandParameter="{Binding ElementName=VerificationCodeTextBox, Path=Text}"
                            Padding="10,5"/>
                </StackPanel>
            </StackPanel>
        </GroupBox>

        <!-- Album List Section -->
        <GroupBox Grid.Row="2" Header="Albums" Margin="0,0,0,10"
                  Visibility="{Binding IsFolderStructureLoaded, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Summary -->
                <Border Grid.Row="0" Background="#F5F5F5" Padding="10" Margin="0,0,0,10" CornerRadius="5">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Orientation="Horizontal">
                            <TextBlock Text="Total albums found: " FontWeight="Bold"/>
                            <TextBlock Text="{Binding TotalAlbumCount}" FontWeight="Bold"/>
                            <TextBlock Text=" (" Margin="5,0,0,0"/>
                            <TextBlock Text="{Binding FolderStructure.TotalImageCount, Mode=OneWay}" FontWeight="Bold"/>
                            <TextBlock Text=" images, "/>
                            <TextBlock Text="{Binding FolderStructure.TotalEstimatedSize, Mode=OneWay}" FontWeight="Bold"/>
                            <TextBlock Text=")"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <Button Content="Select all" Command="{Binding SelectAllAlbumsCommand}"
                                    Padding="8,4" Margin="0,0,5,0" FontSize="12"/>
                            <Button Content="Deselect all" Command="{Binding DeselectAllAlbumsCommand}"
                                    Padding="8,4" FontSize="12"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- Search Box -->
                <TextBox Grid.Row="1" Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                         Padding="8,5" FontSize="14" Margin="0,0,0,10"
                         ToolTip="Search albums by name, description, or path..."/>

                <!-- Selection Summary -->
                <Border Grid.Row="2" Background="#F0F8FF" Padding="10" Margin="0,0,0,10" CornerRadius="3">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <TextBlock Text="Selected: " FontWeight="Bold"/>
                        <TextBlock Text="{Binding SelectedAlbumCount}" FontWeight="Bold" Margin="0,0,5,0"/>
                        <TextBlock Text="albums with " FontWeight="Bold"/>
                        <TextBlock Text="{Binding TotalSelectedImageCount}" FontWeight="Bold" Margin="0,0,5,0"/>
                        <TextBlock Text="images (" FontWeight="Bold"/>
                        <TextBlock Text="{Binding TotalSelectedSize}" FontWeight="Bold"/>
                        <TextBlock Text=")" FontWeight="Bold"/>
                    </StackPanel>
                </Border>

                <!-- Albums ListView with Enhanced Scrolling -->
                <Border Grid.Row="3" BorderBrush="#CCCCCC" BorderThickness="1" CornerRadius="3">
                    <ScrollViewer VerticalScrollBarVisibility="Auto"
                                  HorizontalScrollBarVisibility="Disabled"
                                  CanContentScroll="True">
                        <ListView ItemsSource="{Binding AlbumsView}"
                                  ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                                  ScrollViewer.VerticalScrollBarVisibility="Disabled"
                                  VirtualizingPanel.IsVirtualizing="True"
                                  VirtualizingPanel.VirtualizationMode="Recycling">
                            <ListView.ItemContainerStyle>
                                <Style TargetType="ListViewItem">
                                    <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                    <Setter Property="Padding" Value="2"/>
                                    <Setter Property="Margin" Value="0"/>
                                </Style>
                            </ListView.ItemContainerStyle>
                            <ListView.ItemTemplate>
                                <DataTemplate>
                                    <Grid Margin="3">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Checkbox -->
                                        <CheckBox Grid.Column="0" IsChecked="{Binding IsSelected}"
                                                  VerticalAlignment="Center" Margin="0,0,8,0"/>

                                        <!-- Album Icon -->
                                        <TextBlock Grid.Column="1" Text="{Binding AlbumTypeIcon}"
                                                   FontSize="12" VerticalAlignment="Center" Margin="0,0,6,0"/>

                                        <!-- Album Info -->
                                        <StackPanel Grid.Column="2" VerticalAlignment="Center">
                                            <TextBlock Text="{Binding DisplayName}" FontWeight="Bold" FontSize="11"/>
                                            <TextBlock Text="{Binding Description}" FontSize="9"
                                                       Foreground="Gray" TextTrimming="CharacterEllipsis"
                                                       MaxHeight="12">
                                                <TextBlock.Style>
                                                    <Style TargetType="TextBlock">
                                                        <Setter Property="Visibility" Value="Visible"/>
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding Description}" Value="">
                                                                <Setter Property="Visibility" Value="Collapsed"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding Description}" Value="{x:Null}">
                                                                <Setter Property="Visibility" Value="Collapsed"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </TextBlock.Style>
                                            </TextBlock>
                                            <StackPanel Orientation="Horizontal" Margin="0,1,0,0">
                                                <TextBlock Text="{Binding ImageCount}" FontSize="9" Foreground="Gray"/>
                                                <TextBlock Text=" images" FontSize="9" Foreground="Gray"/>
                                                <TextBlock Text=" • " FontSize="9" Foreground="Gray" Margin="3,0"/>
                                                <TextBlock Text="{Binding EstimatedSize}" FontSize="9" Foreground="Gray"/>
                                            </StackPanel>
                                        </StackPanel>

                                        <!-- Album Status -->
                                        <StackPanel Grid.Column="3" VerticalAlignment="Center" HorizontalAlignment="Right">
                                            <TextBlock Text="✓" FontSize="10" Foreground="Green" FontWeight="Bold"
                                                       Visibility="{Binding IsSelected, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                        </StackPanel>
                                    </Grid>
                                </DataTemplate>
                            </ListView.ItemTemplate>
                        </ListView>
                    </ScrollViewer>
                </Border>

                <!-- Download Selected Button -->
                <Button Grid.Row="4" Content="Download Selected Albums"
                        Margin="0,10,0,0" Padding="10,5"
                        HorizontalAlignment="Left"/>
            </Grid>
        </GroupBox>

        <!-- Download Configuration Section -->
        <GroupBox Grid.Row="3" Header="Download Configuration" Margin="0,0,0,10">
            <StackPanel Margin="10">
                <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                    <TextBlock Text="Target Directory:" VerticalAlignment="Center" Width="120"/>
                    <TextBox Text="{Binding TargetDirectory}" 
                             IsReadOnly="True" 
                             Width="400" Margin="0,0,10,0"/>
                    <Button Content="Browse..." 
                            Command="{Binding SelectTargetDirectoryCommand}"
                            Padding="10,5"/>
                </StackPanel>

                <StackPanel Orientation="Horizontal">
                    <Button Content="Select Albums to Download"
                            Command="{Binding SelectAlbumsForDownloadCommand}"
                            IsEnabled="{Binding IsAuthenticated}"
                            Margin="0,0,10,0" Padding="15,8"
                            Background="#4CAF50" Foreground="White"/>
                    <Button Content="Download All"
                            Command="{Binding StartDownloadCommand}"
                            IsEnabled="{Binding CanStartDownload}"
                            Margin="0,0,10,0" Padding="15,8"/>
                    <Button Content="Pause"
                            Command="{Binding PauseDownloadCommand}"
                            IsEnabled="{Binding CanPauseDownload}"
                            Margin="0,0,10,0" Padding="15,8"/>
                    <Button Content="Resume"
                            Command="{Binding ResumeDownloadCommand}"
                            IsEnabled="{Binding CanResumeDownload}"
                            Margin="0,0,10,0" Padding="15,8"/>
                    <Button Content="Cancel"
                            Command="{Binding CancelDownloadCommand}"
                            IsEnabled="{Binding CanCancelDownload}"
                            Padding="15,8"/>
                </StackPanel>
            </StackPanel>
        </GroupBox>

        <!-- Progress and Log Section -->
        <GroupBox Grid.Row="4" Header="Status and Log">
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Status -->
                <TextBlock Grid.Row="0" Text="{Binding StatusMessage}" 
                           FontWeight="Bold" Margin="0,0,0,10"/>

                <!-- Progress Bars -->
                <StackPanel Grid.Row="1" Margin="0,0,0,10">
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                        <TextBlock Text="Overall Progress:" Margin="0,0,10,0"/>
                        <TextBlock Text="{Binding ProgressText}" FontWeight="Bold"/>
                        <TextBlock Text="{Binding DownloadSpeed, StringFormat=' - {0}'}" Margin="10,0,0,0"/>
                        <TextBlock Text="{Binding EstimatedTimeRemaining, StringFormat=' - ETA: {0}'}" Margin="10,0,0,0"/>
                    </StackPanel>
                    <ProgressBar Height="20" Value="{Binding OverallProgress}" Maximum="100" Margin="0,0,0,10"/>

                    <TextBlock Text="Current File:" Margin="0,0,0,5"/>
                    <ProgressBar Height="20" Value="{Binding CurrentFileProgress}" Maximum="100"/>
                </StackPanel>

                <!-- Log Messages -->
                <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
                    <ListBox ItemsSource="{Binding LogMessages}" 
                             ScrollViewer.HorizontalScrollBarVisibility="Auto"
                             FontFamily="Consolas" FontSize="12">
                        <ListBox.ItemContainerStyle>
                            <Style TargetType="ListBoxItem">
                                <Setter Property="Padding" Value="2"/>
                                <Setter Property="Margin" Value="0"/>
                            </Style>
                        </ListBox.ItemContainerStyle>
                    </ListBox>
                </ScrollViewer>
            </Grid>
        </GroupBox>

        <!-- Status Bar -->
        <StatusBar Grid.Row="5">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock Text="Ready"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
