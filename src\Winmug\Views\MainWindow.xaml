<Window x:Class="Winmug.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Winmug.Converters"
        mc:Ignorable="d"
        Title="Winmug - SmugMug Photo Downloader"
        Height="700" Width="1000"
        MinHeight="600" MinWidth="800"
        WindowStartupLocation="CenterScreen"
        Background="#F8F9FA">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header Section with User Profile -->
        <Border Grid.Row="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1" Padding="20">
            <StackPanel>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- User Profile Section -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center"
                                Visibility="{Binding IsAuthenticated, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <!-- Profile Picture -->
                        <Border Width="64" Height="64" CornerRadius="32" Background="#4CAF50" Margin="0,0,15,0">
                            <TextBlock Text="{Binding UserDisplayName, Converter={x:Static local:FirstLetterConverter.Instance}}"
                                       FontSize="24" FontWeight="Bold" Foreground="White"
                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>

                        <!-- User Info -->
                        <StackPanel VerticalAlignment="Center">
                            <TextBlock Text="{Binding UserDisplayName}" FontSize="20" FontWeight="Bold" Foreground="#2E7D32"/>
                            <TextBlock Text="{Binding UserStatusText}" FontSize="12" Foreground="#666666" Margin="0,2,0,0"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- Authentication Prompt (when not authenticated) -->
                    <StackPanel Grid.Column="1" VerticalAlignment="Center" HorizontalAlignment="Center"
                                Visibility="{Binding IsAuthenticated, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                        <TextBlock Text="Please authenticate to access your SmugMug photos"
                                   FontSize="16" Foreground="#666666" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <Button Content="Authenticate with SmugMug"
                                Command="{Binding InitiateAuthenticationCommand}"
                                Padding="20,10" FontSize="14"
                                Background="#2E7D32" Foreground="White" BorderThickness="0"/>
                    </StackPanel>

                    <!-- Action Buttons (when authenticated) -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center"
                                Visibility="{Binding IsAuthenticated, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <Button Content="Load Folder Structure"
                                Command="{Binding LoadFolderStructureCommand}"
                                Margin="0,0,10,0" Padding="15,8" FontSize="12"
                                Background="#1976D2" Foreground="White" BorderThickness="0"/>
                        <Button Content="Logout"
                                Command="{Binding LogoutCommand}"
                                Padding="15,8" FontSize="12"
                                Background="#F44336" Foreground="White" BorderThickness="0"/>
                    </StackPanel>
                </Grid>

                <!-- Verification Code Input (only visible when waiting) -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0"
                            Visibility="{Binding IsWaitingForVerificationCode, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <TextBlock Text="Enter verification code:" VerticalAlignment="Center" Margin="0,0,10,0" FontSize="14"/>
                    <TextBox x:Name="VerificationCodeTextBox" Width="150" Margin="0,0,10,0" Padding="8,6"/>
                    <Button Content="Submit"
                            Command="{Binding CompleteAuthenticationCommand}"
                            CommandParameter="{Binding ElementName=VerificationCodeTextBox, Path=Text}"
                            Padding="15,6" Background="#4CAF50" Foreground="White" BorderThickness="0"/>
                </StackPanel>

                <!-- Progress Section -->
                <Grid Margin="0,20,0,0"
                      Visibility="{Binding IsLoadingFolderStructure, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Status Message -->
                    <TextBlock Grid.Row="0" Text="{Binding StatusMessage}"
                               FontWeight="Bold" Margin="0,0,0,8" HorizontalAlignment="Center" FontSize="14"/>

                    <!-- Progress Bar -->
                    <ProgressBar Grid.Row="1" Value="{Binding AlbumsFoundCount}"
                                 Maximum="{Binding AlbumsFoundMaximum}"
                                 Height="8" Margin="0,0,0,5"
                                 Background="#E0E0E0" Foreground="#4CAF50"/>

                    <!-- Progress Text -->
                    <TextBlock Grid.Row="2" Text="{Binding AlbumDiscoveryProgress}"
                               HorizontalAlignment="Center" FontSize="12" Foreground="#666666"/>
                </Grid>
            </StackPanel>
        </Border>

        <!-- Main Content Area -->
        <Grid Grid.Row="1" Margin="20"
              Visibility="{Binding IsAuthenticated, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Album Summary Header -->
            <Border Grid.Row="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="1"
                    CornerRadius="8,8,0,0" Padding="20,15"
                    Visibility="{Binding IsFolderStructureLoaded, Converter={StaticResource BooleanToVisibilityConverter}}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Album Count Info -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="Number of albums found: " FontSize="14" Foreground="#666666"/>
                        <TextBlock Text="{Binding TotalAlbumCount}" FontSize="14" FontWeight="Bold"/>
                        <TextBlock Text=" (" FontSize="14" Foreground="#666666" Margin="5,0,0,0"/>
                        <TextBlock Text="{Binding FolderStructure.TotalEstimatedSize, Mode=OneWay}" FontSize="14" FontWeight="Bold"/>
                        <TextBlock Text=")" FontSize="14" Foreground="#666666"/>
                    </StackPanel>

                    <!-- Refresh Button -->
                    <Button Grid.Column="1" Content="🔄" FontSize="16" Width="32" Height="32"
                            Margin="0,0,10,0" Background="Transparent" BorderThickness="1"
                            BorderBrush="#E0E0E0"
                            ToolTip="Refresh albums"/>

                    <!-- Hide Albums Toggle -->
                    <Button Grid.Column="2" Content="Hide albums" FontSize="12" Padding="10,6"
                            Background="Transparent" BorderThickness="1" BorderBrush="#E0E0E0"
                            Foreground="#666666"/>
                </Grid>
            </Border>

            <!-- Save Location -->
            <Border Grid.Row="1" Background="White" BorderBrush="#E0E0E0" BorderThickness="1,0,1,1"
                    Padding="20,15"
                    Visibility="{Binding IsFolderStructureLoaded, Converter={StaticResource BooleanToVisibilityConverter}}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="Save location:" FontSize="14" Foreground="#666666"
                               VerticalAlignment="Center" Margin="0,0,15,0"/>
                    <TextBox Grid.Column="1" Text="{Binding TargetDirectory}" FontSize="14"
                             Background="#F8F9FA" BorderThickness="1" BorderBrush="#E0E0E0"
                             Padding="10,8" VerticalAlignment="Center"/>
                    <Button Grid.Column="2" Content="Change" FontSize="12" Padding="10,8" Margin="10,0,0,0"
                            Command="{Binding SelectTargetDirectoryCommand}"
                            Background="#1976D2" Foreground="White" BorderThickness="0"/>
                </Grid>
            </Border>

            <!-- Download Method -->
            <Border Grid.Row="2" Background="White" BorderBrush="#E0E0E0" BorderThickness="1,0,1,1"
                    Padding="20,15"
                    Visibility="{Binding IsFolderStructureLoaded, Converter={StaticResource BooleanToVisibilityConverter}}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="Select a download method:" FontSize="14" Foreground="#666666"
                               VerticalAlignment="Center" Margin="0,0,15,0"/>
                    <TextBlock Grid.Column="1" Text="Originals" FontSize="14" FontWeight="Bold"
                               VerticalAlignment="Center"/>
                    <TextBlock Grid.Column="2" Text="?" FontSize="14" Foreground="#1976D2"
                               VerticalAlignment="Center" Cursor="Hand"
                               ToolTip="Download original high-resolution images"/>
                </Grid>
            </Border>

            <!-- Albums Section -->
            <Border Grid.Row="3" Background="White" BorderBrush="#E0E0E0" BorderThickness="1,0,1,1"
                    CornerRadius="0,0,8,8"
                    Visibility="{Binding IsFolderStructureLoaded, Converter={StaticResource BooleanToVisibilityConverter}}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Albums Header -->
                    <Border Grid.Row="0" Background="#F8F9FA" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1"
                            Padding="20,15">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- Albums Title and Selection Info -->
                            <StackPanel Grid.Column="0" VerticalAlignment="Center">
                                <TextBlock Text="Albums" FontSize="18" FontWeight="Bold" Margin="0,0,0,5"/>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="Selected: " FontSize="12" Foreground="#666666"/>
                                    <TextBlock Text="{Binding TotalSelectedSize}" FontSize="12" FontWeight="Bold"/>
                                </StackPanel>
                            </StackPanel>

                            <!-- Select All / Deselect All -->
                            <Button Grid.Column="1" Content="Select all" FontSize="12" Padding="10,6" Margin="0,0,10,0"
                                    Command="{Binding SelectAllAlbumsCommand}"
                                    Background="Transparent" BorderThickness="1" BorderBrush="#E0E0E0"
                                    Foreground="#666666"/>
                            <Button Grid.Column="2" Content="Deselect all" FontSize="12" Padding="10,6"
                                    Command="{Binding DeselectAllAlbumsCommand}"
                                    Background="Transparent" BorderThickness="1" BorderBrush="#E0E0E0"
                                    Foreground="#666666"/>
                        </Grid>
                    </Border>

                    <!-- Search Box -->
                    <Border Grid.Row="1" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1"
                            Padding="20,10">
                        <TextBox Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                 FontSize="14" Padding="12,8" Background="#F8F9FA" BorderThickness="1"
                                 BorderBrush="#E0E0E0"
                                 ToolTip="Search albums by name, description, or path...">
                            <TextBox.Style>
                                <Style TargetType="TextBox">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="TextBox">
                                                <Border Background="{TemplateBinding Background}"
                                                        BorderBrush="{TemplateBinding BorderBrush}"
                                                        BorderThickness="{TemplateBinding BorderThickness}"
                                                        CornerRadius="3">
                                                    <Grid>
                                                        <ScrollViewer x:Name="PART_ContentHost"
                                                                      VerticalAlignment="Center"
                                                                      Margin="{TemplateBinding Padding}"/>
                                                        <TextBlock Text="🔍 Search albums..."
                                                                   Foreground="#999999"
                                                                   VerticalAlignment="Center"
                                                                   Margin="{TemplateBinding Padding}"
                                                                   IsHitTestVisible="False">
                                                            <TextBlock.Style>
                                                                <Style TargetType="TextBlock">
                                                                    <Setter Property="Visibility" Value="Collapsed"/>
                                                                    <Style.Triggers>
                                                                        <DataTrigger Binding="{Binding Text, RelativeSource={RelativeSource TemplatedParent}}" Value="">
                                                                            <Setter Property="Visibility" Value="Visible"/>
                                                                        </DataTrigger>
                                                                    </Style.Triggers>
                                                                </Style>
                                                            </TextBlock.Style>
                                                        </TextBlock>
                                                    </Grid>
                                                </Border>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </TextBox.Style>
                        </TextBox>
                    </Border>

                    <!-- Albums List -->
                    <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto"
                                  HorizontalScrollBarVisibility="Disabled" Padding="0">
                        <ItemsControl ItemsSource="{Binding AlbumsView}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border BorderBrush="#F0F0F0" BorderThickness="0,0,0,1" Padding="20,12">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- Checkbox -->
                                            <CheckBox Grid.Column="0" IsChecked="{Binding IsSelected}"
                                                      VerticalAlignment="Center" Margin="0,0,15,0"/>

                                            <!-- Album Icon -->
                                            <Border Grid.Column="1" Width="24" Height="24" Background="#E3F2FD"
                                                    CornerRadius="3" Margin="0,0,15,0">
                                                <TextBlock Text="{Binding AlbumTypeIcon}" FontSize="14"
                                                           HorizontalAlignment="Center" VerticalAlignment="Center"
                                                           Foreground="#1976D2"/>
                                            </Border>

                                            <!-- Album Info -->
                                            <StackPanel Grid.Column="2" VerticalAlignment="Center">
                                                <TextBlock Text="{Binding DisplayName}" FontSize="14" FontWeight="SemiBold"
                                                           Foreground="#333333" Margin="0,0,0,2"/>
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="{Binding ImageCount}" FontSize="12" Foreground="#666666"/>
                                                    <TextBlock Text=" images" FontSize="12" Foreground="#666666"/>
                                                    <TextBlock Text=" • " FontSize="12" Foreground="#666666" Margin="5,0"/>
                                                    <TextBlock Text="{Binding EstimatedSize}" FontSize="12" Foreground="#666666"/>
                                                </StackPanel>
                                            </StackPanel>

                                            <!-- Privacy Status -->
                                            <StackPanel Grid.Column="3" VerticalAlignment="Center" Orientation="Horizontal">
                                                <TextBlock Text="{Binding PrivacyIcon}" FontSize="12" Margin="0,0,5,0"
                                                           ToolTip="{Binding PrivacyStatus}"/>
                                                <TextBlock Text="✓" FontSize="14" Foreground="#4CAF50" FontWeight="Bold"
                                                           Visibility="{Binding IsSelected, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </Grid>
            </Border>

            <!-- Download Button Section -->
            <Border Grid.Row="4" Background="White" Padding="20" Margin="0,20,0,0"
                    BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8"
                    Visibility="{Binding IsFolderStructureLoaded, Converter={StaticResource BooleanToVisibilityConverter}}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Download Progress (when downloading) -->
                    <StackPanel Grid.Column="0" VerticalAlignment="Center"
                                Visibility="{Binding IsDownloading, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                            <TextBlock Text="Downloading..." FontSize="14" FontWeight="Bold" Margin="0,0,10,0"/>
                            <TextBlock Text="{Binding ProgressText}" FontSize="14"/>
                        </StackPanel>
                        <ProgressBar Height="8" Value="{Binding OverallProgress}" Maximum="100"
                                     Background="#E0E0E0" Foreground="#4CAF50"/>
                    </StackPanel>

                    <!-- Status Text (when not downloading) -->
                    <TextBlock Grid.Column="0" Text="Ready to download selected albums"
                               FontSize="14" Foreground="#666666" VerticalAlignment="Center"
                               Visibility="{Binding IsDownloading, Converter={StaticResource InverseBooleanToVisibilityConverter}}"/>

                    <!-- Action Buttons -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <!-- Download Button -->
                        <Button Content="Start download" FontSize="14" Padding="20,12" Margin="0,0,10,0"
                                Command="{Binding StartDownloadCommand}"
                                IsEnabled="{Binding CanStartDownload}"
                                Background="#4CAF50" Foreground="White" BorderThickness="0"
                                Visibility="{Binding IsDownloading, Converter={StaticResource InverseBooleanToVisibilityConverter}}"/>

                        <!-- Pause/Resume Buttons (when downloading) -->
                        <Button Content="Pause" FontSize="14" Padding="15,12" Margin="0,0,10,0"
                                Command="{Binding PauseDownloadCommand}"
                                IsEnabled="{Binding CanPauseDownload}"
                                Background="#FF9800" Foreground="White" BorderThickness="0"
                                Visibility="{Binding IsDownloading, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                        <Button Content="Cancel" FontSize="14" Padding="15,12"
                                Command="{Binding CancelDownloadCommand}"
                                IsEnabled="{Binding CanCancelDownload}"
                                Background="#F44336" Foreground="White" BorderThickness="0"
                                Visibility="{Binding IsDownloading, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                        <!-- Done Button (when not downloading) -->
                        <Button Content="Done" FontSize="14" Padding="15,12"
                                Background="Transparent" BorderThickness="1" BorderBrush="#E0E0E0"
                                Foreground="#666666"
                                Visibility="{Binding IsDownloading, Converter={StaticResource InverseBooleanToVisibilityConverter}}"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>

        <!-- Log Section (Hidden by default, can be shown when needed) -->
        <Expander Grid.Row="1" Header="Show Log" Margin="20,0,20,20"
                  VerticalAlignment="Bottom" HorizontalAlignment="Left"
                  Visibility="{Binding IsAuthenticated, Converter={StaticResource BooleanToVisibilityConverter}}"
                  IsExpanded="False">
            <Border Background="White" BorderBrush="#E0E0E0" BorderThickness="1"
                    CornerRadius="8" Padding="15" MaxHeight="200">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <ItemsControl ItemsSource="{Binding LogMessages}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding}" FontFamily="Consolas" FontSize="11"
                                           Foreground="#666666" Margin="0,1"/>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>
            </Border>
        </Expander>
    </Grid>
</Window>
