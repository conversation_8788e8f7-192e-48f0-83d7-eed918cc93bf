<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:converters="clr-namespace:Winmug.Converters">

    <!-- Converters -->
    <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
    <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
    <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
    <converters:FirstLetterConverter x:Key="FirstLetterConverter"/>

    <!-- Button Styles -->
    <Style TargetType="Button">
        <Setter Property="Padding" Value="10,5"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="MinWidth" Value="80"/>
        <Setter Property="Background" Value="#FF0078D4"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="3">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#FF106EBE"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#FF005A9E"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" Value="#FFCCCCCC"/>
                            <Setter Property="Foreground" Value="#FF666666"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- TextBox Styles -->
    <Style TargetType="TextBox">
        <Setter Property="Padding" Value="5"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="#FFCCCCCC"/>
        <Setter Property="Background" Value="White"/>
        <Style.Triggers>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="BorderBrush" Value="#FF0078D4"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- GroupBox Styles -->
    <Style TargetType="GroupBox">
        <Setter Property="Padding" Value="10"/>
        <Setter Property="Margin" Value="0,5"/>
        <Setter Property="BorderBrush" Value="#FFCCCCCC"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Background" Value="#FFFAFAFA"/>
    </Style>

    <!-- ProgressBar Styles -->
    <Style TargetType="ProgressBar">
        <Setter Property="Height" Value="20"/>
        <Setter Property="Background" Value="#FFE6E6E6"/>
        <Setter Property="Foreground" Value="#FF0078D4"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="#FFCCCCCC"/>
    </Style>

    <!-- StatusBar Styles -->
    <Style TargetType="StatusBar">
        <Setter Property="Background" Value="#FFF0F0F0"/>
        <Setter Property="BorderBrush" Value="#FFCCCCCC"/>
        <Setter Property="BorderThickness" Value="0,1,0,0"/>
    </Style>

</ResourceDictionary>
